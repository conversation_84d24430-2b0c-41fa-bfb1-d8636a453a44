import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Circle } from 'lucide-react';
import { useEditor } from '../../contexts/EditorContext';
import { getLanguageFromExtension } from '../../utils/fileUtils';

const getLanguageIcon = (language: string): string => {
  switch (language) {
    case 'javascript':
      return '🟨';
    case 'typescript':
      return '🔷';
    case 'python':
      return '🐍';
    case 'html':
      return '🌐';
    case 'css':
      return '🎨';
    case 'json':
      return '📋';
    case 'markdown':
      return '📝';
    default:
      return '📄';
  }
};

export const TabBar: React.FC = () => {
  const { state, closeTab, setActiveTab } = useEditor();

  if (state.tabs.length === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className="flex bg-editor-sidebar dark:bg-editor-sidebar border-b border-editor-border dark:border-editor-border overflow-x-auto editor-scrollbar"
      style={{ minHeight: '40px' }}
    >
      <AnimatePresence mode="popLayout">
        {state.tabs.map((tab) => (
          <motion.div
            key={tab.id}
            initial={{ opacity: 0, scale: 0.8, x: -20 }}
            animate={{ opacity: 1, scale: 1, x: 0 }}
            exit={{ opacity: 0, scale: 0.8, x: -20 }}
            transition={{ duration: 0.2 }}
            className={`
              flex items-center px-3 py-2 border-r border-editor-border dark:border-editor-border
              cursor-pointer group relative min-w-0 max-w-xs
              ${tab.isActive 
                ? 'bg-editor-bg dark:bg-editor-bg text-editor-text dark:text-editor-text' 
                : 'bg-editor-sidebar dark:bg-editor-sidebar text-editor-text/70 dark:text-editor-text/70 hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50'
              }
              transition-all duration-150
            `}
            onClick={() => setActiveTab(tab.id)}
          >
            {/* Language Icon */}
            <span className="mr-2 text-xs flex-shrink-0">
              {getLanguageIcon(tab.language)}
            </span>

            {/* File Name */}
            <span className="text-sm truncate flex-1 min-w-0">
              {tab.name}
            </span>

            {/* Dirty Indicator or Close Button */}
            <div className="ml-2 flex-shrink-0 flex items-center">
              {tab.isDirty ? (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="w-2 h-2 bg-blue-400 rounded-full mr-1"
                />
              ) : null}
              
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation();
                  closeTab(tab.id);
                }}
                className={`
                  p-1 rounded hover:bg-red-500/20 transition-colors
                  ${tab.isActive ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}
                `}
              >
                <X size={12} className="text-editor-text/60 dark:text-editor-text/60 hover:text-red-400" />
              </motion.button>
            </div>

            {/* Active Tab Indicator */}
            {tab.isActive && (
              <motion.div
                layoutId="activeTab"
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-editor-accent"
                transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
              />
            )}
          </motion.div>
        ))}
      </AnimatePresence>

      {/* New Tab Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="px-3 py-2 text-editor-text/60 dark:text-editor-text/60 hover:text-editor-text dark:hover:text-editor-text hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50 transition-colors"
        onClick={() => {
          // This would open a new file dialog
          console.log('New file');
        }}
      >
        <span className="text-lg">+</span>
      </motion.button>
    </motion.div>
  );
};
