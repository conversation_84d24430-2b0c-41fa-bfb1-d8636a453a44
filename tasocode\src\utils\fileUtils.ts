import { FileItem, SupportedLanguage } from '../types';

export const getLanguageFromExtension = (filename: string): SupportedLanguage => {
  const ext = filename.split('.').pop()?.toLowerCase();
  
  switch (ext) {
    case 'js':
    case 'jsx':
      return 'javascript';
    case 'ts':
    case 'tsx':
      return 'typescript';
    case 'py':
      return 'python';
    case 'html':
    case 'htm':
      return 'html';
    case 'css':
      return 'css';
    case 'json':
      return 'json';
    case 'md':
    case 'markdown':
      return 'markdown';
    case 'xml':
      return 'xml';
    case 'yml':
    case 'yaml':
      return 'yaml';
    case 'sql':
      return 'sql';
    default:
      return 'plaintext';
  }
};

export const getFileIcon = (file: FileItem): string => {
  if (file.type === 'folder') {
    return file.isOpen ? '📂' : '📁';
  }
  
  const language = getLanguageFromExtension(file.name);
  
  switch (language) {
    case 'javascript':
      return '🟨';
    case 'typescript':
      return '🔷';
    case 'python':
      return '🐍';
    case 'html':
      return '🌐';
    case 'css':
      return '🎨';
    case 'json':
      return '📋';
    case 'markdown':
      return '📝';
    case 'xml':
      return '📄';
    case 'yaml':
      return '⚙️';
    case 'sql':
      return '🗃️';
    default:
      return '📄';
  }
};

export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

export const createSampleFiles = (): FileItem[] => {
  return [
    {
      id: generateId(),
      name: 'src',
      path: '/src',
      type: 'folder',
      isOpen: true,
      children: [
        {
          id: generateId(),
          name: 'components',
          path: '/src/components',
          type: 'folder',
          isOpen: false,
          children: [
            {
              id: generateId(),
              name: 'Button.tsx',
              path: '/src/components/Button.tsx',
              type: 'file',
              content: `import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  disabled = false
}) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={\`btn btn-\${variant} \${disabled ? 'disabled' : ''}\`}
    >
      {children}
    </button>
  );
};`,
              language: 'typescript'
            }
          ]
        },
        {
          id: generateId(),
          name: 'utils',
          path: '/src/utils',
          type: 'folder',
          isOpen: false,
          children: [
            {
              id: generateId(),
              name: 'helpers.js',
              path: '/src/utils/helpers.js',
              type: 'file',
              content: `// Utility functions for common operations

export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

export const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date));
};`,
              language: 'javascript'
            }
          ]
        },
        {
          id: generateId(),
          name: 'App.tsx',
          path: '/src/App.tsx',
          type: 'file',
          content: `import React from 'react';
import { Button } from './components/Button';
import './App.css';

function App() {
  const handleClick = () => {
    console.log('Button clicked!');
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to TasoCode</h1>
        <p>A modern code editor built with React and TypeScript</p>
        <Button onClick={handleClick} variant="primary">
          Get Started
        </Button>
      </header>
    </div>
  );
}

export default App;`,
          language: 'typescript'
        }
      ]
    },
    {
      id: generateId(),
      name: 'styles',
      path: '/styles',
      type: 'folder',
      isOpen: false,
      children: [
        {
          id: generateId(),
          name: 'main.css',
          path: '/styles/main.css',
          type: 'file',
          content: `/* Main stylesheet for TasoCode */

:root {
  --primary-color: #007acc;
  --secondary-color: #1e1e1e;
  --text-color: #cccccc;
  --background-color: #252526;
  --border-color: #3c3c3c;
}

body {
  font-family: 'JetBrains Mono', monospace;
  background-color: var(--background-color);
  color: var(--text-color);
  margin: 0;
  padding: 0;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #1177bb;
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
}`,
          language: 'css'
        }
      ]
    },
    {
      id: generateId(),
      name: 'README.md',
      path: '/README.md',
      type: 'file',
      content: `# TasoCode

A modern, feature-rich code editor built with React, TypeScript, and Monaco Editor.

## Features

- 🎨 **Modern UI** - Clean, professional interface with dark/light themes
- 📁 **File Explorer** - Navigate your project structure with ease
- 📝 **Multi-tab Editing** - Work on multiple files simultaneously
- 🔍 **Search & Replace** - Powerful find and replace functionality
- 🎯 **Syntax Highlighting** - Support for multiple programming languages
- ⚡ **Fast Performance** - Built with Vite for lightning-fast development
- 🔧 **Extensible** - Designed to be easily extended with plugins

## Supported Languages

- JavaScript/TypeScript
- Python
- HTML/CSS
- JSON
- Markdown
- XML/YAML
- SQL
- And more...

## Getting Started

1. Clone the repository
2. Install dependencies: \`npm install\`
3. Start the development server: \`npm run dev\`
4. Open your browser and start coding!

## Keyboard Shortcuts

- \`Ctrl+N\` - New file
- \`Ctrl+O\` - Open file
- \`Ctrl+S\` - Save file
- \`Ctrl+F\` - Find
- \`Ctrl+H\` - Replace
- \`Ctrl+/\` - Toggle comment
- \`F11\` - Toggle fullscreen

## Contributing

We welcome contributions! Please see our contributing guidelines for more information.

## License

MIT License - see LICENSE file for details.`,
      language: 'markdown'
    }
  ];
};
