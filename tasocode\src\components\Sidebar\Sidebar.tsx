import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Files, 
  Search, 
  GitBranch, 
  Settings, 
  Menu,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { useEditor } from '../../contexts/EditorContext';
import { FileExplorer } from '../FileExplorer/FileExplorer';
import { ThemeToggle } from '../ThemeToggle/ThemeToggle';

type SidebarTab = 'explorer' | 'search' | 'git' | 'settings';

export const Sidebar: React.FC = () => {
  const { state, toggleSidebar, setSidebarWidth, toggleSearch } = useEditor();
  const [activeTab, setActiveTab] = useState<SidebarTab>('explorer');
  const [isResizing, setIsResizing] = useState(false);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  };

  React.useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const newWidth = Math.max(200, Math.min(600, e.clientX));
      setSidebarWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, setSidebarWidth]);

  const sidebarTabs = [
    { id: 'explorer' as SidebarTab, icon: Files, label: 'Explorer' },
    { id: 'search' as SidebarTab, icon: Search, label: 'Search' },
    { id: 'git' as SidebarTab, icon: GitBranch, label: 'Source Control' },
    { id: 'settings' as SidebarTab, icon: Settings, label: 'Settings' },
  ];

  const handleTabClick = (tabId: SidebarTab) => {
    if (tabId === 'search') {
      toggleSearch();
    }
    setActiveTab(tabId);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'explorer':
        return <FileExplorer />;
      case 'search':
        return (
          <div className="p-4 text-center text-editor-text/70 dark:text-editor-text/70">
            <Search size={48} className="mx-auto mb-4 opacity-50" />
            <p>Search functionality is available in the top panel</p>
          </div>
        );
      case 'git':
        return (
          <div className="p-4 text-center text-editor-text/70 dark:text-editor-text/70">
            <GitBranch size={48} className="mx-auto mb-4 opacity-50" />
            <p>Git integration coming soon</p>
          </div>
        );
      case 'settings':
        return (
          <div className="p-4 space-y-4">
            <h3 className="text-sm font-semibold text-editor-text dark:text-editor-text uppercase tracking-wide mb-4">
              Settings
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-editor-text dark:text-editor-text">Theme</span>
                <ThemeToggle />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-editor-text dark:text-editor-text">Font Size</span>
                <select className="px-2 py-1 bg-editor-bg dark:bg-editor-bg border border-editor-border dark:border-editor-border rounded text-sm text-editor-text dark:text-editor-text">
                  <option>12px</option>
                  <option selected>14px</option>
                  <option>16px</option>
                  <option>18px</option>
                </select>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-editor-text dark:text-editor-text">Tab Size</span>
                <select className="px-2 py-1 bg-editor-bg dark:bg-editor-bg border border-editor-border dark:border-editor-border rounded text-sm text-editor-text dark:text-editor-text">
                  <option selected>2</option>
                  <option>4</option>
                  <option>8</option>
                </select>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  if (!state.showSidebar) {
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="flex flex-col bg-editor-sidebar dark:bg-editor-sidebar border-r border-editor-border dark:border-editor-border"
        style={{ width: '48px' }}
      >
        <div className="flex flex-col items-center py-2 space-y-1">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleSidebar}
            className="p-2 rounded hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50 transition-colors"
            title="Show Sidebar"
          >
            <ChevronRight size={16} className="text-editor-text/70 dark:text-editor-text/70" />
          </motion.button>
          
          {sidebarTabs.map((tab) => (
            <motion.button
              key={tab.id}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => handleTabClick(tab.id)}
              className={`p-2 rounded transition-colors ${
                activeTab === tab.id
                  ? 'bg-editor-accent text-white'
                  : 'text-editor-text/70 dark:text-editor-text/70 hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50'
              }`}
              title={tab.label}
            >
              <tab.icon size={16} />
            </motion.button>
          ))}
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="flex bg-editor-sidebar dark:bg-editor-sidebar border-r border-editor-border dark:border-editor-border relative"
      style={{ width: `${state.sidebarWidth}px` }}
    >
      {/* Tab Icons */}
      <div className="flex flex-col bg-editor-sidebar dark:bg-editor-sidebar border-r border-editor-border dark:border-editor-border" style={{ width: '48px' }}>
        <div className="flex flex-col items-center py-2 space-y-1">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleSidebar}
            className="p-2 rounded hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50 transition-colors"
            title="Hide Sidebar"
          >
            <ChevronLeft size={16} className="text-editor-text/70 dark:text-editor-text/70" />
          </motion.button>
          
          {sidebarTabs.map((tab) => (
            <motion.button
              key={tab.id}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => handleTabClick(tab.id)}
              className={`p-2 rounded transition-colors ${
                activeTab === tab.id
                  ? 'bg-editor-accent text-white'
                  : 'text-editor-text/70 dark:text-editor-text/70 hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50'
              }`}
              title={tab.label}
            >
              <tab.icon size={16} />
            </motion.button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.2 }}
            className="h-full"
          >
            {renderTabContent()}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Resize Handle */}
      <div
        className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize hover:bg-editor-accent/50 transition-colors"
        onMouseDown={handleMouseDown}
      />
    </motion.div>
  );
};
