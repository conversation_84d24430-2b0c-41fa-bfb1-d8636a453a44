{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/pgsql/pgsql.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/pgsql/pgsql.ts\nvar conf = {\n  comments: {\n    lineComment: \"--\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sql\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    // This list is generated using `keywords.js`\n    \"ALL\",\n    \"ANALYSE\",\n    \"ANALYZE\",\n    \"AND\",\n    \"ANY\",\n    \"ARRAY\",\n    \"AS\",\n    \"ASC\",\n    \"ASYMMETRIC\",\n    \"AUTHORIZATION\",\n    \"BINARY\",\n    \"BOTH\",\n    \"CASE\",\n    \"CAST\",\n    \"CHECK\",\n    \"COLLATE\",\n    \"COLLATION\",\n    \"COLUMN\",\n    \"CONCURRENTLY\",\n    \"CONSTRAINT\",\n    \"CREATE\",\n    \"CROSS\",\n    \"CURRENT_CATALOG\",\n    \"CURRENT_DATE\",\n    \"CURRENT_ROLE\",\n    \"CURRENT_SCHEMA\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"DEFAULT\",\n    \"DEFERRABLE\",\n    \"DESC\",\n    \"DISTINCT\",\n    \"DO\",\n    \"ELSE\",\n    \"END\",\n    \"EXCEPT\",\n    \"FALSE\",\n    \"FETCH\",\n    \"FOR\",\n    \"FOREIGN\",\n    \"FREEZE\",\n    \"FROM\",\n    \"FULL\",\n    \"GRANT\",\n    \"GROUP\",\n    \"HAVING\",\n    \"ILIKE\",\n    \"IN\",\n    \"INITIALLY\",\n    \"INNER\",\n    \"INTERSECT\",\n    \"INTO\",\n    \"IS\",\n    \"ISNULL\",\n    \"JOIN\",\n    \"LATERAL\",\n    \"LEADING\",\n    \"LEFT\",\n    \"LIKE\",\n    \"LIMIT\",\n    \"LOCALTIME\",\n    \"LOCALTIMESTAMP\",\n    \"NATURAL\",\n    \"NOT\",\n    \"NOTNULL\",\n    \"NULL\",\n    \"OFFSET\",\n    \"ON\",\n    \"ONLY\",\n    \"OR\",\n    \"ORDER\",\n    \"OUTER\",\n    \"OVERLAPS\",\n    \"PLACING\",\n    \"PRIMARY\",\n    \"REFERENCES\",\n    \"RETURNING\",\n    \"RIGHT\",\n    \"SELECT\",\n    \"SESSION_USER\",\n    \"SIMILAR\",\n    \"SOME\",\n    \"SYMMETRIC\",\n    \"TABLE\",\n    \"TABLESAMPLE\",\n    \"THEN\",\n    \"TO\",\n    \"TRAILING\",\n    \"TRUE\",\n    \"UNION\",\n    \"UNIQUE\",\n    \"USER\",\n    \"USING\",\n    \"VARIADIC\",\n    \"VERBOSE\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WINDOW\",\n    \"WITH\"\n  ],\n  operators: [\n    \"AND\",\n    \"BETWEEN\",\n    \"IN\",\n    \"LIKE\",\n    \"NOT\",\n    \"OR\",\n    \"IS\",\n    \"NULL\",\n    \"INTERSECT\",\n    \"UNION\",\n    \"INNER\",\n    \"JOIN\",\n    \"LEFT\",\n    \"OUTER\",\n    \"RIGHT\"\n  ],\n  builtinFunctions: [\n    \"abbrev\",\n    \"abs\",\n    \"acldefault\",\n    \"aclexplode\",\n    \"acos\",\n    \"acosd\",\n    \"acosh\",\n    \"age\",\n    \"any\",\n    \"area\",\n    \"array_agg\",\n    \"array_append\",\n    \"array_cat\",\n    \"array_dims\",\n    \"array_fill\",\n    \"array_length\",\n    \"array_lower\",\n    \"array_ndims\",\n    \"array_position\",\n    \"array_positions\",\n    \"array_prepend\",\n    \"array_remove\",\n    \"array_replace\",\n    \"array_to_json\",\n    \"array_to_string\",\n    \"array_to_tsvector\",\n    \"array_upper\",\n    \"ascii\",\n    \"asin\",\n    \"asind\",\n    \"asinh\",\n    \"atan\",\n    \"atan2\",\n    \"atan2d\",\n    \"atand\",\n    \"atanh\",\n    \"avg\",\n    \"bit\",\n    \"bit_and\",\n    \"bit_count\",\n    \"bit_length\",\n    \"bit_or\",\n    \"bit_xor\",\n    \"bool_and\",\n    \"bool_or\",\n    \"bound_box\",\n    \"box\",\n    \"brin_desummarize_range\",\n    \"brin_summarize_new_values\",\n    \"brin_summarize_range\",\n    \"broadcast\",\n    \"btrim\",\n    \"cardinality\",\n    \"cbrt\",\n    \"ceil\",\n    \"ceiling\",\n    \"center\",\n    \"char_length\",\n    \"character_length\",\n    \"chr\",\n    \"circle\",\n    \"clock_timestamp\",\n    \"coalesce\",\n    \"col_description\",\n    \"concat\",\n    \"concat_ws\",\n    \"convert\",\n    \"convert_from\",\n    \"convert_to\",\n    \"corr\",\n    \"cos\",\n    \"cosd\",\n    \"cosh\",\n    \"cot\",\n    \"cotd\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"cume_dist\",\n    \"current_catalog\",\n    \"current_database\",\n    \"current_date\",\n    \"current_query\",\n    \"current_role\",\n    \"current_schema\",\n    \"current_schemas\",\n    \"current_setting\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"current_user\",\n    \"currval\",\n    \"cursor_to_xml\",\n    \"cursor_to_xmlschema\",\n    \"date_bin\",\n    \"date_part\",\n    \"date_trunc\",\n    \"database_to_xml\",\n    \"database_to_xml_and_xmlschema\",\n    \"database_to_xmlschema\",\n    \"decode\",\n    \"degrees\",\n    \"dense_rank\",\n    \"diagonal\",\n    \"diameter\",\n    \"div\",\n    \"encode\",\n    \"enum_first\",\n    \"enum_last\",\n    \"enum_range\",\n    \"every\",\n    \"exp\",\n    \"extract\",\n    \"factorial\",\n    \"family\",\n    \"first_value\",\n    \"floor\",\n    \"format\",\n    \"format_type\",\n    \"gcd\",\n    \"gen_random_uuid\",\n    \"generate_series\",\n    \"generate_subscripts\",\n    \"get_bit\",\n    \"get_byte\",\n    \"get_current_ts_config\",\n    \"gin_clean_pending_list\",\n    \"greatest\",\n    \"grouping\",\n    \"has_any_column_privilege\",\n    \"has_column_privilege\",\n    \"has_database_privilege\",\n    \"has_foreign_data_wrapper_privilege\",\n    \"has_function_privilege\",\n    \"has_language_privilege\",\n    \"has_schema_privilege\",\n    \"has_sequence_privilege\",\n    \"has_server_privilege\",\n    \"has_table_privilege\",\n    \"has_tablespace_privilege\",\n    \"has_type_privilege\",\n    \"height\",\n    \"host\",\n    \"hostmask\",\n    \"inet_client_addr\",\n    \"inet_client_port\",\n    \"inet_merge\",\n    \"inet_same_family\",\n    \"inet_server_addr\",\n    \"inet_server_port\",\n    \"initcap\",\n    \"isclosed\",\n    \"isempty\",\n    \"isfinite\",\n    \"isopen\",\n    \"json_agg\",\n    \"json_array_elements\",\n    \"json_array_elements_text\",\n    \"json_array_length\",\n    \"json_build_array\",\n    \"json_build_object\",\n    \"json_each\",\n    \"json_each_text\",\n    \"json_extract_path\",\n    \"json_extract_path_text\",\n    \"json_object\",\n    \"json_object_agg\",\n    \"json_object_keys\",\n    \"json_populate_record\",\n    \"json_populate_recordset\",\n    \"json_strip_nulls\",\n    \"json_to_record\",\n    \"json_to_recordset\",\n    \"json_to_tsvector\",\n    \"json_typeof\",\n    \"jsonb_agg\",\n    \"jsonb_array_elements\",\n    \"jsonb_array_elements_text\",\n    \"jsonb_array_length\",\n    \"jsonb_build_array\",\n    \"jsonb_build_object\",\n    \"jsonb_each\",\n    \"jsonb_each_text\",\n    \"jsonb_extract_path\",\n    \"jsonb_extract_path_text\",\n    \"jsonb_insert\",\n    \"jsonb_object\",\n    \"jsonb_object_agg\",\n    \"jsonb_object_keys\",\n    \"jsonb_path_exists\",\n    \"jsonb_path_match\",\n    \"jsonb_path_query\",\n    \"jsonb_path_query_array\",\n    \"jsonb_path_exists_tz\",\n    \"jsonb_path_query_first\",\n    \"jsonb_path_query_array_tz\",\n    \"jsonb_path_query_first_tz\",\n    \"jsonb_path_query_tz\",\n    \"jsonb_path_match_tz\",\n    \"jsonb_populate_record\",\n    \"jsonb_populate_recordset\",\n    \"jsonb_pretty\",\n    \"jsonb_set\",\n    \"jsonb_set_lax\",\n    \"jsonb_strip_nulls\",\n    \"jsonb_to_record\",\n    \"jsonb_to_recordset\",\n    \"jsonb_to_tsvector\",\n    \"jsonb_typeof\",\n    \"justify_days\",\n    \"justify_hours\",\n    \"justify_interval\",\n    \"lag\",\n    \"last_value\",\n    \"lastval\",\n    \"lcm\",\n    \"lead\",\n    \"least\",\n    \"left\",\n    \"length\",\n    \"line\",\n    \"ln\",\n    \"localtime\",\n    \"localtimestamp\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"lower_inc\",\n    \"lower_inf\",\n    \"lpad\",\n    \"lseg\",\n    \"ltrim\",\n    \"macaddr8_set7bit\",\n    \"make_date\",\n    \"make_interval\",\n    \"make_time\",\n    \"make_timestamp\",\n    \"make_timestamptz\",\n    \"makeaclitem\",\n    \"masklen\",\n    \"max\",\n    \"md5\",\n    \"min\",\n    \"min_scale\",\n    \"mod\",\n    \"mode\",\n    \"multirange\",\n    \"netmask\",\n    \"network\",\n    \"nextval\",\n    \"normalize\",\n    \"now\",\n    \"npoints\",\n    \"nth_value\",\n    \"ntile\",\n    \"nullif\",\n    \"num_nonnulls\",\n    \"num_nulls\",\n    \"numnode\",\n    \"obj_description\",\n    \"octet_length\",\n    \"overlay\",\n    \"parse_ident\",\n    \"path\",\n    \"pclose\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"pg_advisory_lock\",\n    \"pg_advisory_lock_shared\",\n    \"pg_advisory_unlock\",\n    \"pg_advisory_unlock_all\",\n    \"pg_advisory_unlock_shared\",\n    \"pg_advisory_xact_lock\",\n    \"pg_advisory_xact_lock_shared\",\n    \"pg_backend_pid\",\n    \"pg_backup_start_time\",\n    \"pg_blocking_pids\",\n    \"pg_cancel_backend\",\n    \"pg_client_encoding\",\n    \"pg_collation_actual_version\",\n    \"pg_collation_is_visible\",\n    \"pg_column_compression\",\n    \"pg_column_size\",\n    \"pg_conf_load_time\",\n    \"pg_control_checkpoint\",\n    \"pg_control_init\",\n    \"pg_control_recovery\",\n    \"pg_control_system\",\n    \"pg_conversion_is_visible\",\n    \"pg_copy_logical_replication_slot\",\n    \"pg_copy_physical_replication_slot\",\n    \"pg_create_logical_replication_slot\",\n    \"pg_create_physical_replication_slot\",\n    \"pg_create_restore_point\",\n    \"pg_current_logfile\",\n    \"pg_current_snapshot\",\n    \"pg_current_wal_flush_lsn\",\n    \"pg_current_wal_insert_lsn\",\n    \"pg_current_wal_lsn\",\n    \"pg_current_xact_id\",\n    \"pg_current_xact_id_if_assigned\",\n    \"pg_current_xlog_flush_location\",\n    \"pg_current_xlog_insert_location\",\n    \"pg_current_xlog_location\",\n    \"pg_database_size\",\n    \"pg_describe_object\",\n    \"pg_drop_replication_slot\",\n    \"pg_event_trigger_ddl_commands\",\n    \"pg_event_trigger_dropped_objects\",\n    \"pg_event_trigger_table_rewrite_oid\",\n    \"pg_event_trigger_table_rewrite_reason\",\n    \"pg_export_snapshot\",\n    \"pg_filenode_relation\",\n    \"pg_function_is_visible\",\n    \"pg_get_catalog_foreign_keys\",\n    \"pg_get_constraintdef\",\n    \"pg_get_expr\",\n    \"pg_get_function_arguments\",\n    \"pg_get_function_identity_arguments\",\n    \"pg_get_function_result\",\n    \"pg_get_functiondef\",\n    \"pg_get_indexdef\",\n    \"pg_get_keywords\",\n    \"pg_get_object_address\",\n    \"pg_get_owned_sequence\",\n    \"pg_get_ruledef\",\n    \"pg_get_serial_sequence\",\n    \"pg_get_statisticsobjdef\",\n    \"pg_get_triggerdef\",\n    \"pg_get_userbyid\",\n    \"pg_get_viewdef\",\n    \"pg_get_wal_replay_pause_state\",\n    \"pg_has_role\",\n    \"pg_identify_object\",\n    \"pg_identify_object_as_address\",\n    \"pg_import_system_collations\",\n    \"pg_index_column_has_property\",\n    \"pg_index_has_property\",\n    \"pg_indexam_has_property\",\n    \"pg_indexes_size\",\n    \"pg_is_in_backup\",\n    \"pg_is_in_recovery\",\n    \"pg_is_other_temp_schema\",\n    \"pg_is_wal_replay_paused\",\n    \"pg_is_xlog_replay_paused\",\n    \"pg_jit_available\",\n    \"pg_last_committed_xact\",\n    \"pg_last_wal_receive_lsn\",\n    \"pg_last_wal_replay_lsn\",\n    \"pg_last_xact_replay_timestamp\",\n    \"pg_last_xlog_receive_location\",\n    \"pg_last_xlog_replay_location\",\n    \"pg_listening_channels\",\n    \"pg_log_backend_memory_contexts\",\n    \"pg_logical_emit_message\",\n    \"pg_logical_slot_get_binary_changes\",\n    \"pg_logical_slot_get_changes\",\n    \"pg_logical_slot_peek_binary_changes\",\n    \"pg_logical_slot_peek_changes\",\n    \"pg_ls_archive_statusdir\",\n    \"pg_ls_dir\",\n    \"pg_ls_logdir\",\n    \"pg_ls_tmpdir\",\n    \"pg_ls_waldir\",\n    \"pg_mcv_list_items\",\n    \"pg_my_temp_schema\",\n    \"pg_notification_queue_usage\",\n    \"pg_opclass_is_visible\",\n    \"pg_operator_is_visible\",\n    \"pg_opfamily_is_visible\",\n    \"pg_options_to_table\",\n    \"pg_partition_ancestors\",\n    \"pg_partition_root\",\n    \"pg_partition_tree\",\n    \"pg_postmaster_start_time\",\n    \"pg_promote\",\n    \"pg_read_binary_file\",\n    \"pg_read_file\",\n    \"pg_relation_filenode\",\n    \"pg_relation_filepath\",\n    \"pg_relation_size\",\n    \"pg_reload_conf\",\n    \"pg_replication_origin_advance\",\n    \"pg_replication_origin_create\",\n    \"pg_replication_origin_drop\",\n    \"pg_replication_origin_oid\",\n    \"pg_replication_origin_progress\",\n    \"pg_replication_origin_session_is_setup\",\n    \"pg_replication_origin_session_progress\",\n    \"pg_replication_origin_session_reset\",\n    \"pg_replication_origin_session_setup\",\n    \"pg_replication_origin_xact_reset\",\n    \"pg_replication_origin_xact_setup\",\n    \"pg_replication_slot_advance\",\n    \"pg_rotate_logfile\",\n    \"pg_safe_snapshot_blocking_pids\",\n    \"pg_size_bytes\",\n    \"pg_size_pretty\",\n    \"pg_sleep\",\n    \"pg_sleep_for\",\n    \"pg_sleep_until\",\n    \"pg_snapshot_xip\",\n    \"pg_snapshot_xmax\",\n    \"pg_snapshot_xmin\",\n    \"pg_start_backup\",\n    \"pg_stat_file\",\n    \"pg_statistics_obj_is_visible\",\n    \"pg_stop_backup\",\n    \"pg_switch_wal\",\n    \"pg_switch_xlog\",\n    \"pg_table_is_visible\",\n    \"pg_table_size\",\n    \"pg_tablespace_databases\",\n    \"pg_tablespace_location\",\n    \"pg_tablespace_size\",\n    \"pg_terminate_backend\",\n    \"pg_total_relation_size\",\n    \"pg_trigger_depth\",\n    \"pg_try_advisory_lock\",\n    \"pg_try_advisory_lock_shared\",\n    \"pg_try_advisory_xact_lock\",\n    \"pg_try_advisory_xact_lock_shared\",\n    \"pg_ts_config_is_visible\",\n    \"pg_ts_dict_is_visible\",\n    \"pg_ts_parser_is_visible\",\n    \"pg_ts_template_is_visible\",\n    \"pg_type_is_visible\",\n    \"pg_typeof\",\n    \"pg_visible_in_snapshot\",\n    \"pg_wal_lsn_diff\",\n    \"pg_wal_replay_pause\",\n    \"pg_wal_replay_resume\",\n    \"pg_walfile_name\",\n    \"pg_walfile_name_offset\",\n    \"pg_xact_commit_timestamp\",\n    \"pg_xact_commit_timestamp_origin\",\n    \"pg_xact_status\",\n    \"pg_xlog_location_diff\",\n    \"pg_xlog_replay_pause\",\n    \"pg_xlog_replay_resume\",\n    \"pg_xlogfile_name\",\n    \"pg_xlogfile_name_offset\",\n    \"phraseto_tsquery\",\n    \"pi\",\n    \"plainto_tsquery\",\n    \"point\",\n    \"polygon\",\n    \"popen\",\n    \"position\",\n    \"power\",\n    \"pqserverversion\",\n    \"query_to_xml\",\n    \"query_to_xml_and_xmlschema\",\n    \"query_to_xmlschema\",\n    \"querytree\",\n    \"quote_ident\",\n    \"quote_literal\",\n    \"quote_nullable\",\n    \"radians\",\n    \"radius\",\n    \"random\",\n    \"range_agg\",\n    \"range_intersect_agg\",\n    \"range_merge\",\n    \"rank\",\n    \"regexp_count\",\n    \"regexp_instr\",\n    \"regexp_like\",\n    \"regexp_match\",\n    \"regexp_matches\",\n    \"regexp_replace\",\n    \"regexp_split_to_array\",\n    \"regexp_split_to_table\",\n    \"regexp_substr\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"repeat\",\n    \"replace\",\n    \"reverse\",\n    \"right\",\n    \"round\",\n    \"row_number\",\n    \"row_security_active\",\n    \"row_to_json\",\n    \"rpad\",\n    \"rtrim\",\n    \"scale\",\n    \"schema_to_xml\",\n    \"schema_to_xml_and_xmlschema\",\n    \"schema_to_xmlschema\",\n    \"session_user\",\n    \"set_bit\",\n    \"set_byte\",\n    \"set_config\",\n    \"set_masklen\",\n    \"setseed\",\n    \"setval\",\n    \"setweight\",\n    \"sha224\",\n    \"sha256\",\n    \"sha384\",\n    \"sha512\",\n    \"shobj_description\",\n    \"sign\",\n    \"sin\",\n    \"sind\",\n    \"sinh\",\n    \"slope\",\n    \"split_part\",\n    \"sprintf\",\n    \"sqrt\",\n    \"starts_with\",\n    \"statement_timestamp\",\n    \"stddev\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"string_agg\",\n    \"string_to_array\",\n    \"string_to_table\",\n    \"strip\",\n    \"strpos\",\n    \"substr\",\n    \"substring\",\n    \"sum\",\n    \"suppress_redundant_updates_trigger\",\n    \"table_to_xml\",\n    \"table_to_xml_and_xmlschema\",\n    \"table_to_xmlschema\",\n    \"tan\",\n    \"tand\",\n    \"tanh\",\n    \"text\",\n    \"timeofday\",\n    \"timezone\",\n    \"to_ascii\",\n    \"to_char\",\n    \"to_date\",\n    \"to_hex\",\n    \"to_json\",\n    \"to_number\",\n    \"to_regclass\",\n    \"to_regcollation\",\n    \"to_regnamespace\",\n    \"to_regoper\",\n    \"to_regoperator\",\n    \"to_regproc\",\n    \"to_regprocedure\",\n    \"to_regrole\",\n    \"to_regtype\",\n    \"to_timestamp\",\n    \"to_tsquery\",\n    \"to_tsvector\",\n    \"transaction_timestamp\",\n    \"translate\",\n    \"trim\",\n    \"trim_array\",\n    \"trim_scale\",\n    \"trunc\",\n    \"ts_debug\",\n    \"ts_delete\",\n    \"ts_filter\",\n    \"ts_headline\",\n    \"ts_lexize\",\n    \"ts_parse\",\n    \"ts_rank\",\n    \"ts_rank_cd\",\n    \"ts_rewrite\",\n    \"ts_stat\",\n    \"ts_token_type\",\n    \"tsquery_phrase\",\n    \"tsvector_to_array\",\n    \"tsvector_update_trigger\",\n    \"tsvector_update_trigger_column\",\n    \"txid_current\",\n    \"txid_current_if_assigned\",\n    \"txid_current_snapshot\",\n    \"txid_snapshot_xip\",\n    \"txid_snapshot_xmax\",\n    \"txid_snapshot_xmin\",\n    \"txid_status\",\n    \"txid_visible_in_snapshot\",\n    \"unistr\",\n    \"unnest\",\n    \"upper\",\n    \"upper_inc\",\n    \"upper_inf\",\n    \"user\",\n    \"var_pop\",\n    \"var_samp\",\n    \"variance\",\n    \"version\",\n    \"websearch_to_tsquery\",\n    \"width\",\n    \"width_bucket\",\n    \"xml_is_well_formed\",\n    \"xml_is_well_formed_content\",\n    \"xml_is_well_formed_document\",\n    \"xmlagg\",\n    \"xmlcomment\",\n    \"xmlconcat\",\n    \"xmlelement\",\n    \"xmlexists\",\n    \"xmlforest\",\n    \"xmlparse\",\n    \"xmlpi\",\n    \"xmlroot\",\n    \"xmlserialize\",\n    \"xpath\",\n    \"xpath_exists\"\n  ],\n  builtinVariables: [\n    // NOT SUPPORTED\n  ],\n  pseudoColumns: [\n    // NOT SUPPORTED\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/--+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      // Not supporting nested comments, as nested comments seem to not be standard?\n      // i.e. http://stackoverflow.com/questions/728172/are-there-multiline-comment-delimiters-in-sql-that-are-vendor-agnostic\n      // [/\\/\\*/, { token: 'comment.quote', next: '@push' }],    // nested comment not allowed :-(\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [[/'/, { token: \"string\", next: \"@string\" }]],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [[/\"/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]],\n    quotedIdentifier: [\n      [/[^\"]+/, \"identifier\"],\n      [/\"\"/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    scopes: [\n      // NOT SUPPORTED\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA;AAAA,IAER;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA;AAAA,EAElB;AAAA,EACA,eAAe;AAAA;AAAA,EAEf;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,sBAAsB;AAAA,MACjC,EAAE,SAAS,UAAU;AAAA,MACrB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,QAAQ,WAAW;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,qBAAqB;AAAA,YACrB,qBAAqB;AAAA,YACrB,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,oBAAoB,UAAU;AAAA,IACjC;AAAA,IACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,IAC7B,UAAU;AAAA,MACR,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,WAAW,CAAC;AAAA,IACvD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,SAAS;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,kBAAkB;AAAA,YAClB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,qBAAqB,QAAQ;AAAA,MAC9B,CAAC,uBAAuB,QAAQ;AAAA,MAChC,CAAC,2CAA2C,QAAQ;AAAA,IACtD;AAAA,IACA,SAAS,CAAC,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC,CAAC;AAAA,IACrD,QAAQ;AAAA,MACN,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,QAAQ;AAAA,MACf,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,oBAAoB,CAAC,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,oBAAoB,CAAC,CAAC;AAAA,IACpF,kBAAkB;AAAA,MAChB,CAAC,SAAS,YAAY;AAAA,MACtB,CAAC,MAAM,YAAY;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,IACA,QAAQ;AAAA;AAAA,IAER;AAAA,EACF;AACF;", "names": []}