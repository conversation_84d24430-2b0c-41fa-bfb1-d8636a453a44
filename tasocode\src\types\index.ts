export interface FileItem {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'folder';
  children?: FileItem[];
  isOpen?: boolean;
  content?: string;
  language?: string;
}

export interface Tab {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  isDirty: boolean;
  isActive: boolean;
}

export interface EditorState {
  tabs: Tab[];
  activeTabId: string | null;
  files: FileItem[];
  theme: 'light' | 'dark';
  sidebarWidth: number;
  showSidebar: boolean;
  searchQuery: string;
  replaceQuery: string;
  showSearch: boolean;
}

export interface SearchResult {
  line: number;
  column: number;
  text: string;
  match: string;
}

export interface EditorPosition {
  lineNumber: number;
  column: number;
}

export interface EditorSelection {
  startLineNumber: number;
  startColumn: number;
  endLineNumber: number;
  endColumn: number;
}

export type SupportedLanguage = 
  | 'javascript'
  | 'typescript'
  | 'python'
  | 'html'
  | 'css'
  | 'json'
  | 'markdown'
  | 'xml'
  | 'yaml'
  | 'sql'
  | 'plaintext';

export interface EditorContextType {
  state: EditorState;
  openFile: (file: FileItem) => void;
  closeTab: (tabId: string) => void;
  setActiveTab: (tabId: string) => void;
  updateTabContent: (tabId: string, content: string) => void;
  toggleTheme: () => void;
  toggleSidebar: () => void;
  setSidebarWidth: (width: number) => void;
  setSearchQuery: (query: string) => void;
  setReplaceQuery: (query: string) => void;
  toggleSearch: () => void;
  createNewFile: (name: string, path: string) => void;
  saveFile: (tabId: string) => void;
}
