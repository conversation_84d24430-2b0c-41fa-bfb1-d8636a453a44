@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@layer base {
  * {
    box-sizing: border-box;
  }

  html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: 'JetBrains Mono', monospace;
    overflow: hidden;
  }

  #root {
    height: 100vh;
    width: 100vw;
  }
}

@layer components {
  .editor-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .editor-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .editor-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  .editor-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .dark .editor-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }

  .light .editor-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}
