import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Replace, X, ChevronDown, ChevronRight } from 'lucide-react';
import { useEditor } from '../../contexts/EditorContext';

export const SearchPanel: React.FC = () => {
  const { state, setSearchQuery, setReplaceQuery, toggleSearch } = useEditor();
  const [showReplace, setShowReplace] = useState(false);
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [wholeWord, setWholeWord] = useState(false);
  const [useRegex, setUseRegex] = useState(false);

  if (!state.showSearch) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        exit={{ opacity: 0, height: 0 }}
        transition={{ duration: 0.2 }}
        className="bg-editor-sidebar dark:bg-editor-sidebar border-b border-editor-border dark:border-editor-border overflow-hidden"
      >
        <div className="p-4 space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Search size={16} className="text-editor-text/70 dark:text-editor-text/70" />
              <span className="text-sm font-medium text-editor-text dark:text-editor-text">
                Find and Replace
              </span>
            </div>
            <button
              onClick={toggleSearch}
              className="p-1 hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50 rounded transition-colors"
            >
              <X size={14} className="text-editor-text/70 dark:text-editor-text/70" />
            </button>
          </div>

          {/* Search Input */}
          <div className="space-y-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Find"
                value={state.searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-3 py-2 bg-editor-bg dark:bg-editor-bg border border-editor-border dark:border-editor-border rounded text-sm text-editor-text dark:text-editor-text placeholder-editor-text/50 dark:placeholder-editor-text/50 focus:outline-none focus:border-editor-accent"
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                <button
                  onClick={() => setCaseSensitive(!caseSensitive)}
                  className={`px-1 py-0.5 text-xs rounded transition-colors ${
                    caseSensitive 
                      ? 'bg-editor-accent text-white' 
                      : 'text-editor-text/60 dark:text-editor-text/60 hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50'
                  }`}
                  title="Match Case"
                >
                  Aa
                </button>
                <button
                  onClick={() => setWholeWord(!wholeWord)}
                  className={`px-1 py-0.5 text-xs rounded transition-colors ${
                    wholeWord 
                      ? 'bg-editor-accent text-white' 
                      : 'text-editor-text/60 dark:text-editor-text/60 hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50'
                  }`}
                  title="Match Whole Word"
                >
                  Ab
                </button>
                <button
                  onClick={() => setUseRegex(!useRegex)}
                  className={`px-1 py-0.5 text-xs rounded transition-colors ${
                    useRegex 
                      ? 'bg-editor-accent text-white' 
                      : 'text-editor-text/60 dark:text-editor-text/60 hover:bg-editor-bg/50 dark:hover:bg-editor-bg/50'
                  }`}
                  title="Use Regular Expression"
                >
                  .*
                </button>
              </div>
            </div>

            {/* Replace Toggle */}
            <button
              onClick={() => setShowReplace(!showReplace)}
              className="flex items-center space-x-1 text-sm text-editor-text/70 dark:text-editor-text/70 hover:text-editor-text dark:hover:text-editor-text transition-colors"
            >
              {showReplace ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
              <Replace size={14} />
              <span>Replace</span>
            </button>

            {/* Replace Input */}
            <AnimatePresence>
              {showReplace && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <input
                    type="text"
                    placeholder="Replace"
                    value={state.replaceQuery}
                    onChange={(e) => setReplaceQuery(e.target.value)}
                    className="w-full px-3 py-2 bg-editor-bg dark:bg-editor-bg border border-editor-border dark:border-editor-border rounded text-sm text-editor-text dark:text-editor-text placeholder-editor-text/50 dark:placeholder-editor-text/50 focus:outline-none focus:border-editor-accent"
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-3 py-1.5 bg-editor-accent text-white text-sm rounded hover:bg-editor-accent-hover transition-colors"
            >
              Find All
            </motion.button>
            {showReplace && (
              <>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-3 py-1.5 bg-editor-bg dark:bg-editor-bg border border-editor-border dark:border-editor-border text-editor-text dark:text-editor-text text-sm rounded hover:bg-editor-bg/80 dark:hover:bg-editor-bg/80 transition-colors"
                >
                  Replace
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-3 py-1.5 bg-editor-bg dark:bg-editor-bg border border-editor-border dark:border-editor-border text-editor-text dark:text-editor-text text-sm rounded hover:bg-editor-bg/80 dark:hover:bg-editor-bg/80 transition-colors"
                >
                  Replace All
                </motion.button>
              </>
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
