import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { EditorProvider } from './contexts/EditorContext';
import { useTheme } from './hooks/useTheme';
import { Sidebar } from './components/Sidebar/Sidebar';
import { TabBar } from './components/TabBar/TabBar';
import { Editor } from './components/Editor/Editor';
import { StatusBar } from './components/StatusBar/StatusBar';
import { SearchPanel } from './components/SearchPanel/SearchPanel';

const AppContent: React.FC = () => {
  const { theme } = useTheme();

  useEffect(() => {
    // Apply theme class to document
    document.documentElement.className = theme;
  }, [theme]);

  return (
    <EditorProvider>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className={`h-screen w-screen flex flex-col overflow-hidden ${theme}`}
      >
        {/* Search Panel */}
        <SearchPanel />

        {/* Main Content */}
        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <Sidebar />

          {/* Editor Area */}
          <div className="flex flex-col flex-1 overflow-hidden">
            {/* Tab Bar */}
            <TabBar />

            {/* Editor */}
            <Editor />
          </div>
        </div>

        {/* Status Bar */}
        <StatusBar />
      </motion.div>
    </EditorProvider>
  );
};

function App() {
  return <AppContent />;
}

export default App;
