import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { GitBranch, Zap, AlertCircle, CheckCircle } from 'lucide-react';
import { useEditor } from '../../contexts/EditorContext';

export const StatusBar: React.FC = () => {
  const { state } = useEditor();
  const [time, setTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const activeTab = state.tabs.find(tab => tab.id === state.activeTabId);
  const totalLines = activeTab ? activeTab.content.split('\n').length : 0;
  const totalChars = activeTab ? activeTab.content.length : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className="flex items-center justify-between px-4 py-1 bg-editor-accent text-white text-xs border-t border-editor-border dark:border-editor-border"
      style={{ height: '24px' }}
    >
      {/* Left side */}
      <div className="flex items-center space-x-4">
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="flex items-center space-x-1 cursor-pointer"
        >
          <GitBranch size={12} />
          <span>main</span>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.05 }}
          className="flex items-center space-x-1 cursor-pointer"
        >
          <CheckCircle size={12} className="text-green-400" />
          <span>No issues</span>
        </motion.div>

        {activeTab && (
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
          >
            <span>{activeTab.language.toUpperCase()}</span>
            <span>•</span>
            <span>UTF-8</span>
            <span>•</span>
            <span>LF</span>
          </motion.div>
        )}
      </div>

      {/* Right side */}
      <div className="flex items-center space-x-4">
        {activeTab && (
          <motion.div
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
          >
            <span>{totalLines} lines</span>
            <span>•</span>
            <span>{totalChars} chars</span>
          </motion.div>
        )}

        <motion.div
          whileHover={{ scale: 1.05 }}
          className="flex items-center space-x-1 cursor-pointer"
        >
          <Zap size={12} className="text-yellow-400" />
          <span>TasoCode</span>
        </motion.div>

        <span className="font-mono">
          {time.toLocaleTimeString()}
        </span>
      </div>
    </motion.div>
  );
};
