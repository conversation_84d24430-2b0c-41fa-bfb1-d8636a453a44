import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight, ChevronDown, File, Folder, FolderOpen } from 'lucide-react';
import { FileItem } from '../../types';
import { useEditor } from '../../contexts/EditorContext';
import { getFileIcon } from '../../utils/fileUtils';

interface FileTreeItemProps {
  file: FileItem;
  level: number;
  onToggle: (fileId: string) => void;
}

const FileTreeItem: React.FC<FileTreeItemProps> = ({ file, level, onToggle }) => {
  const { openFile } = useEditor();

  const handleClick = () => {
    if (file.type === 'folder') {
      onToggle(file.id);
    } else {
      openFile(file);
    }
  };

  const handleDoubleClick = () => {
    if (file.type === 'file') {
      openFile(file);
    }
  };

  return (
    <div>
      <motion.div
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.2, delay: level * 0.05 }}
        className={`
          flex items-center py-1 px-2 cursor-pointer hover:bg-white/5 dark:hover:bg-white/5
          transition-colors duration-150 group
        `}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
        onDoubleClick={handleDoubleClick}
      >
        {file.type === 'folder' && (
          <motion.div
            animate={{ rotate: file.isOpen ? 90 : 0 }}
            transition={{ duration: 0.2 }}
            className="mr-1 text-editor-text/60 dark:text-editor-text/60"
          >
            <ChevronRight size={14} />
          </motion.div>
        )}
        
        <div className="mr-2 text-sm">
          {file.type === 'folder' ? (
            file.isOpen ? <FolderOpen size={16} className="text-blue-400" /> : <Folder size={16} className="text-blue-500" />
          ) : (
            <File size={16} className="text-editor-text/70 dark:text-editor-text/70" />
          )}
        </div>
        
        <span className="text-sm text-editor-text dark:text-editor-text group-hover:text-white transition-colors">
          {file.name}
        </span>
      </motion.div>

      <AnimatePresence>
        {file.type === 'folder' && file.isOpen && file.children && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            {file.children.map((child) => (
              <FileTreeItem
                key={child.id}
                file={child}
                level={level + 1}
                onToggle={onToggle}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export const FileExplorer: React.FC = () => {
  const { state } = useEditor();
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set(['src']) // Start with src folder expanded
  );

  const toggleFolder = (fileId: string) => {
    const updateFileTree = (files: FileItem[]): FileItem[] => {
      return files.map(file => {
        if (file.id === fileId && file.type === 'folder') {
          const newIsOpen = !file.isOpen;
          if (newIsOpen) {
            setExpandedFolders(prev => new Set([...prev, fileId]));
          } else {
            setExpandedFolders(prev => {
              const newSet = new Set(prev);
              newSet.delete(fileId);
              return newSet;
            });
          }
          return { ...file, isOpen: newIsOpen };
        }
        if (file.children) {
          return { ...file, children: updateFileTree(file.children) };
        }
        return file;
      });
    };

    // This would normally update the global state
    // For now, we'll just handle the visual state locally
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className="h-full bg-editor-sidebar dark:bg-editor-sidebar border-r border-editor-border dark:border-editor-border"
    >
      <div className="p-3 border-b border-editor-border dark:border-editor-border">
        <h3 className="text-sm font-semibold text-editor-text dark:text-editor-text uppercase tracking-wide">
          Explorer
        </h3>
      </div>
      
      <div className="overflow-y-auto editor-scrollbar" style={{ height: 'calc(100% - 60px)' }}>
        <div className="py-2">
          {state.files.map((file) => (
            <FileTreeItem
              key={file.id}
              file={file}
              level={0}
              onToggle={toggleFolder}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};
