import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { EditorState, EditorContextType, FileItem, Tab } from '../types';
import { generateId, getLanguageFromExtension, createSampleFiles } from '../utils/fileUtils';

const initialState: EditorState = {
  tabs: [],
  activeTabId: null,
  files: createSampleFiles(),
  theme: 'dark',
  sidebarWidth: 250,
  showSidebar: true,
  searchQuery: '',
  replaceQuery: '',
  showSearch: false,
};

type EditorAction =
  | { type: 'OPEN_FILE'; payload: FileItem }
  | { type: 'CLOSE_TAB'; payload: string }
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'UPDATE_TAB_CONTENT'; payload: { tabId: string; content: string } }
  | { type: 'TOGGLE_THEME' }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_SIDEBAR_WIDTH'; payload: number }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_REPLACE_QUERY'; payload: string }
  | { type: 'TOGGLE_SEARCH' }
  | { type: 'CREATE_NEW_FILE'; payload: { name: string; path: string } }
  | { type: 'SAVE_FILE'; payload: string };

const editorReducer = (state: EditorState, action: EditorAction): EditorState => {
  switch (action.type) {
    case 'OPEN_FILE': {
      const file = action.payload;
      if (file.type === 'folder') return state;

      const existingTab = state.tabs.find(tab => tab.path === file.path);
      if (existingTab) {
        return {
          ...state,
          activeTabId: existingTab.id,
        };
      }

      const newTab: Tab = {
        id: generateId(),
        name: file.name,
        path: file.path,
        content: file.content || '',
        language: file.language || getLanguageFromExtension(file.name),
        isDirty: false,
        isActive: true,
      };

      return {
        ...state,
        tabs: [...state.tabs.map(tab => ({ ...tab, isActive: false })), newTab],
        activeTabId: newTab.id,
      };
    }

    case 'CLOSE_TAB': {
      const tabId = action.payload;
      const tabs = state.tabs.filter(tab => tab.id !== tabId);
      let activeTabId = state.activeTabId;

      if (state.activeTabId === tabId) {
        activeTabId = tabs.length > 0 ? tabs[tabs.length - 1].id : null;
      }

      return {
        ...state,
        tabs: tabs.map(tab => ({
          ...tab,
          isActive: tab.id === activeTabId,
        })),
        activeTabId,
      };
    }

    case 'SET_ACTIVE_TAB': {
      const tabId = action.payload;
      return {
        ...state,
        tabs: state.tabs.map(tab => ({
          ...tab,
          isActive: tab.id === tabId,
        })),
        activeTabId: tabId,
      };
    }

    case 'UPDATE_TAB_CONTENT': {
      const { tabId, content } = action.payload;
      return {
        ...state,
        tabs: state.tabs.map(tab =>
          tab.id === tabId
            ? { ...tab, content, isDirty: true }
            : tab
        ),
      };
    }

    case 'TOGGLE_THEME':
      return {
        ...state,
        theme: state.theme === 'light' ? 'dark' : 'light',
      };

    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        showSidebar: !state.showSidebar,
      };

    case 'SET_SIDEBAR_WIDTH':
      return {
        ...state,
        sidebarWidth: action.payload,
      };

    case 'SET_SEARCH_QUERY':
      return {
        ...state,
        searchQuery: action.payload,
      };

    case 'SET_REPLACE_QUERY':
      return {
        ...state,
        replaceQuery: action.payload,
      };

    case 'TOGGLE_SEARCH':
      return {
        ...state,
        showSearch: !state.showSearch,
      };

    case 'CREATE_NEW_FILE': {
      const { name, path } = action.payload;
      const newTab: Tab = {
        id: generateId(),
        name,
        path,
        content: '',
        language: getLanguageFromExtension(name),
        isDirty: true,
        isActive: true,
      };

      return {
        ...state,
        tabs: [...state.tabs.map(tab => ({ ...tab, isActive: false })), newTab],
        activeTabId: newTab.id,
      };
    }

    case 'SAVE_FILE': {
      const tabId = action.payload;
      return {
        ...state,
        tabs: state.tabs.map(tab =>
          tab.id === tabId
            ? { ...tab, isDirty: false }
            : tab
        ),
      };
    }

    default:
      return state;
  }
};

const EditorContext = createContext<EditorContextType | undefined>(undefined);

export const EditorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(editorReducer, initialState);

  const openFile = useCallback((file: FileItem) => {
    dispatch({ type: 'OPEN_FILE', payload: file });
  }, []);

  const closeTab = useCallback((tabId: string) => {
    dispatch({ type: 'CLOSE_TAB', payload: tabId });
  }, []);

  const setActiveTab = useCallback((tabId: string) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tabId });
  }, []);

  const updateTabContent = useCallback((tabId: string, content: string) => {
    dispatch({ type: 'UPDATE_TAB_CONTENT', payload: { tabId, content } });
  }, []);

  const toggleTheme = useCallback(() => {
    dispatch({ type: 'TOGGLE_THEME' });
  }, []);

  const toggleSidebar = useCallback(() => {
    dispatch({ type: 'TOGGLE_SIDEBAR' });
  }, []);

  const setSidebarWidth = useCallback((width: number) => {
    dispatch({ type: 'SET_SIDEBAR_WIDTH', payload: width });
  }, []);

  const setSearchQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });
  }, []);

  const setReplaceQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_REPLACE_QUERY', payload: query });
  }, []);

  const toggleSearch = useCallback(() => {
    dispatch({ type: 'TOGGLE_SEARCH' });
  }, []);

  const createNewFile = useCallback((name: string, path: string) => {
    dispatch({ type: 'CREATE_NEW_FILE', payload: { name, path } });
  }, []);

  const saveFile = useCallback((tabId: string) => {
    dispatch({ type: 'SAVE_FILE', payload: tabId });
  }, []);

  const value: EditorContextType = {
    state,
    openFile,
    closeTab,
    setActiveTab,
    updateTabContent,
    toggleTheme,
    toggleSidebar,
    setSidebarWidth,
    setSearchQuery,
    setReplaceQuery,
    toggleSearch,
    createNewFile,
    saveFile,
  };

  return (
    <EditorContext.Provider value={value}>
      {children}
    </EditorContext.Provider>
  );
};

export const useEditor = (): EditorContextType => {
  const context = useContext(EditorContext);
  if (!context) {
    throw new Error('useEditor must be used within an EditorProvider');
  }
  return context;
};
